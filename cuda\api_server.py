#!/usr/bin/env python3
"""
FastAPI web server for SDXL image generation.
Provides REST API endpoints matching the Swift implementation.
"""

import os
import sys
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
import uvicorn
import argparse

try:
    # Try relative imports first (when run as module)
    from .models import (
        GenerationRequest, GenerationResponse, StatusResponse,
        ErrorResponse, HealthResponse, ModelInfo
    )
    from .generation_manager import GenerationManager
    from .image_generator import find_safetensors_file
except ImportError:
    # Fall back to absolute imports (when run directly)
    from models import (
        GenerationRequest, GenerationResponse, StatusResponse,
        ErrorResponse, HealthResponse, ModelInfo
    )
    from generation_manager import GenerationManager
    from image_generator import find_safetensors_file


# Global generation manager
generation_manager: GenerationManager = None


def create_app(model_path: str, device: str = "auto") -> FastAPI:
    """Create and configure the FastAPI application"""
    async def lifespan(app: FastAPI):
        await generation_manager.initialize()
        print("🚀 Image generation manager ready!")
        yield
        print("🛑 Shutting down...")
        await generation_manager.cleanup()
        print("🛑 Cleanup completed")
    
    
    app = FastAPI(
        title="SDXL/Pony Image Generation API",
        description="REST API for generating images using Stable Diffusion XL and Pony Diffusion models",
        version="1.0.0",
        lifespan=lifespan,
    )
    
    # Add CORS middleware
    # app.add_middleware(
    #    CORSMiddleware,
    #    allow_origins=["*"],  # In production, specify actual origins
    #    allow_credentials=True,
    #    allow_methods=["*"],
    #    allow_headers=["*"],
    # )
    
    # Initialize generation manager
    global generation_manager
    generation_manager = GenerationManager(model_path, device)
    
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """Health check endpoint"""
        model_info = None
        if generation_manager and generation_manager.generator:
            model_data = generation_manager.generator.get_model_info()
            model_info = ModelInfo(**model_data)

        return HealthResponse(
            status="healthy",
            service="image-generation",
            model_info=model_info
        )

    @app.get("/model", response_model=ModelInfo)
    async def get_model_info():
        """Get information about the loaded model"""
        if not generation_manager or not generation_manager.generator:
            raise HTTPException(status_code=503, detail="Model not loaded")

        model_data = generation_manager.generator.get_model_info()
        return ModelInfo(**model_data)
    
    @app.get("/")
    async def serve_frontend():
        """Serve the frontend HTML page"""
        with open("web_frontend/index.html") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content, media_type="text/html")
    

    @app.post("/generate", response_model=GenerationResponse)
    async def start_generation(
        request: GenerationRequest,
        background_tasks: BackgroundTasks
    ):
        """
        Start image generation.
        
        Args:
            request: Generation request parameters
            
        Returns:
            Generation response with unique ID
            
        Raises:
            HTTPException: If validation fails or generation cannot be started
        """
        try:
            generation_id = await generation_manager.start_generation(request)
            
            # Schedule cleanup of old generations in background
            background_tasks.add_task(
                generation_manager.cleanup_old_generations
            )
            
            return GenerationResponse(id=generation_id)
            
        except RuntimeError as e:
            if "already in progress" in str(e):
                raise HTTPException(
                    status_code=409,
                    detail=ErrorResponse(
                        error=str(e),
                        code=409
                    )
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=ErrorResponse(
                        error=str(e),
                        code=500
                    )
                )
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    error=f"Invalid request: {str(e)}",
                    code=400
                )
            )


    @app.get("/generation/{generation_id}", response_model=StatusResponse)
    async def get_generation_status(generation_id: str):
        """
        Check generation status and get results.

        Args:
            generation_id: Unique generation ID

        Returns:
            Status response with current status and results if complete

        Raises:
            HTTPException: If generation ID is not found
        """
        try:
            status = generation_manager.get_status(generation_id)
            return status

        except KeyError:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error=f"Generation with ID {generation_id} not found",
                    code=404
                ).model_dump()
            )
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error=f"Internal server error: {str(e)}",
                    code=500
                ).model_dump()
            )


    @app.delete("/generation/{generation_id}", status_code=204)
    async def clear_generation(generation_id: str):
        """
        Clear completed generation from memory.
        
        Args:
            generation_id: Unique generation ID
            
        Returns:
            204 No Content if successful
            
        Raises:
            HTTPException: If generation ID is not found or still pending
        """
        try:
            cleared = await generation_manager.clear_generation(generation_id)
            
            if cleared:
                return None
            else:
                raise HTTPException(
                    status_code=404,
                    detail=ErrorResponse(
                        error=f"Generation with ID {generation_id} not found or still pending",
                        code=404
                    )
                )
                
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error=f"Internal server error: {str(e)}",
                    code=500
                )
            )


    @app.get("/status")
    async def get_server_status():
        """Get server status and device information"""
        return {
            "status": "running",
            "generation_in_progress": generation_manager.is_generation_in_progress(),
            "device_info": generation_manager.get_device_info()
        }

    @app.post("/cleanup")
    async def force_cleanup():
        """Force cleanup of old generations and memory (manual memory management)"""
        try:
            await generation_manager.cleanup_old_generations(max_age_hours=0)  # Clean all completed
            return {"status": "cleanup_completed", "message": "Memory cleanup performed"}
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error=f"Cleanup failed: {str(e)}",
                    code=500
                ).model_dump()
            )

    
    return app

def main():
    """Main function to run the API server"""
    parser = argparse.ArgumentParser(description="SDXL/Pony Image Generation API Server")
    parser.add_argument(
        "--model-path",
        type=str,
        help="Path to the SDXL or Pony safetensors file"
    )
    parser.add_argument(
        "--host",
        type=str,
        default="127.0.0.1",
        help="Host to bind the server to (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8074,
        help="Port to bind the server to (default: 8074)"
    )
    parser.add_argument(
        "--device",
        type=str,
        default="auto",
        choices=["auto", "cuda", "cpu"],
        help="Device to use for generation (default: auto)"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    
    args = parser.parse_args()
    
    # Find model file if not specified
    model_path = args.model_path
    if not model_path:
        print("No model path specified. Searching for .safetensors files...")
        model_path = find_safetensors_file(".")
        if not model_path:
            print("Error: No .safetensors file found. Please specify --model-path")
            sys.exit(1)
        print(f"Found model file: {model_path}")
    
    # Verify model file exists
    if not os.path.exists(model_path):
        print(f"Error: Model file not found: {model_path}")
        sys.exit(1)
    
    # Reconfigure the app with the model path
    global app
    app = create_app(model_path, args.device)
    
    print("=== SDXL/Pony Image Generation API Server ===")
    print(f"Model: {model_path}")
    print(f"Device: {args.device}")
    print(f"Server: http://{args.host}:{args.port}")
    print(f"Docs: http://{args.host}:{args.port}/docs")
    print(f"Model Info: http://{args.host}:{args.port}/model")
    print("=" * 45)
    
    # Run the server
    uvicorn.run(
        "cuda.api_server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level="warning"
    )


if __name__ == "__main__":
    main()

