#!/usr/bin/env python3
"""
Image generation module for SDXL using PyTorch and CUDA.
Refactored from generate_image.py for use in the FastAPI service.
Supports both regular SDXL and Pony Diffusion models.
"""

import torch
import os
import base64
import io
import gc
from pathlib import Path
from diffusers import StableDiffusionXLPipeline
from PIL import Image
from typing import Optional, List, Callable, Literal


# Set environment variable to disable symlinks warning on Windows
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

# Model type definitions
ModelType = Literal["sdxl", "pony"]


class ImageGenerator:
    """SDXL Image Generator class for handling pipeline and generation"""

    def __init__(self, model_path: str, device: str = "auto"):
        """
        Initialize the image generator.

        Args:
            model_path: Path to the SDXL safetensors file
            device: Device to use ("cuda", "cpu", or "auto")
        """
        self.model_path = model_path
        self.device = self._determine_device(device)
        self.model_type = self._detect_model_type(model_path)
        self.pipeline = None
        self._load_pipeline()
    
    def _determine_device(self, device: str) -> str:
        """Determine the best device to use"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device

    def _detect_model_type(self, model_path: str) -> ModelType:
        """
        Detect the model type based on filename patterns.

        Args:
            model_path: Path to the model file

        Returns:
            Model type ("sdxl" or "pony")
        """
        filename = Path(model_path).name.lower()

        # Check for Pony model indicators in filename
        pony_indicators = [
            "pony", "ponydiffusion", "pony_diffusion", "ponyv6", "pony_v6",
            "pdxl", "pony-diffusion", "ponydiff", "ponyxl"
        ]

        for indicator in pony_indicators:
            if indicator in filename:
                print(f"🐴 Detected Pony model: {filename}")
                return "pony"

        print(f"🎨 Detected SDXL model: {filename}")
        return "sdxl"
    
    def _load_pipeline(self):
        """Load the SDXL pipeline from the safetensors file"""
        model_type_name = "Pony Diffusion" if self.model_type == "pony" else "SDXL"
        print(f"Loading {model_type_name} model from: {self.model_path}")

        # Verify model file exists
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model file not found: {self.model_path}")

        # Prepare pipeline loading arguments
        pipeline_args = {
            "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
            "use_safetensors": True,
            "safety_checker": None,
            "feature_extractor": None
        }

        # Add Pony-specific configurations
        if self.model_type == "pony":
            # Pony models work better with CLIP skip 2
            pipeline_args["clip_skip"] = 2
            print("🐴 Applying Pony model optimizations (CLIP skip 2)")

        # First try offline mode
        try:
            print("Attempting to load pipeline in offline mode...")

            # Set offline mode to prevent any downloads
            os.environ["HF_HUB_OFFLINE"] = "1"
            os.environ["TRANSFORMERS_OFFLINE"] = "1"

            self.pipeline = StableDiffusionXLPipeline.from_single_file(
                self.model_path,
                local_files_only=True,
                **pipeline_args
            )

            print("✓ Successfully loaded in offline mode!")

        except Exception as offline_error:
            print(f"Offline mode failed: {offline_error}")
            print("Trying with minimal online components (config files only)...")

            # Clear offline environment variables
            if "HF_HUB_OFFLINE" in os.environ:
                del os.environ["HF_HUB_OFFLINE"]
            if "TRANSFORMERS_OFFLINE" in os.environ:
                del os.environ["TRANSFORMERS_OFFLINE"]

            try:
                self.pipeline = StableDiffusionXLPipeline.from_single_file(
                    self.model_path,
                    **pipeline_args
                )
                print("✓ Successfully loaded with minimal online components!")

            except Exception as online_error:
                raise RuntimeError(f"Failed to load pipeline: {online_error}")

        # Move to the appropriate device and optimize
        self._setup_pipeline()
    
    def _setup_pipeline(self):
        """Setup and optimize the pipeline"""
        try:
            # Move to the appropriate device
            print(f"Moving pipeline to {self.device}...")
            self.pipeline = self.pipeline.to(self.device)
            
            # Enable memory efficient settings
            if self.device == "cuda":
                print("Enabling CUDA optimizations...")
                self.pipeline.enable_attention_slicing()
                try:
                    self.pipeline.enable_model_cpu_offload()
                except Exception as e:
                    print(f"Warning: Could not enable model CPU offload: {e}")
            else:
                print("Using CPU optimizations...")
                self.pipeline.enable_attention_slicing()
            
            print(f"Pipeline loaded successfully on {self.device}")
            
        except Exception as e:
            raise RuntimeError(f"Error setting up pipeline: {e}")
    
    def _enhance_prompt_for_pony(self, prompt: str) -> str:
        """
        Enhance prompt for Pony models by adding quality score tags if not present.

        Args:
            prompt: Original prompt

        Returns:
            Enhanced prompt with quality tags
        """
        if self.model_type != "pony":
            return prompt

        # Check if quality scores are already present
        quality_indicators = ["score_"]
        has_quality_tags = any(indicator in prompt.lower() for indicator in quality_indicators)

        if not has_quality_tags:
            # Add quality score tags at the beginning
            quality_prefix = "score_9, score_8_up, score_7_up, "
            enhanced_prompt = quality_prefix + prompt
            print("🐴 Enhanced Pony prompt with quality tags")
            return enhanced_prompt

        return prompt

    def _get_default_guidance_scale(self) -> float:
        """Get default guidance scale based on model type"""
        if self.model_type == "pony":
            return 4.5  # Pony models work better with lower guidance scale
        return 7.5  # Standard SDXL default

    def generate_images(
        self,
        prompt: str,
        negative_prompt: str = "",
        image_count: int = 1,
        step_count: int = 20,
        guidance_scale: float = 7.5,
        seed: int = 0,
        height: int = 1024,
        width: int = 1024,
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[str]:
        """
        Generate images and return them as base64 encoded PNG strings.

        Args:
            prompt: Text prompt to guide image generation
            negative_prompt: Negative prompt to guide what to avoid
            image_count: Number of images to generate
            step_count: Number of inference steps
            guidance_scale: Guidance scale for generation
            seed: Random seed for reproducible results
            height: Image height in pixels
            width: Image width in pixels
            progress_callback: Optional callback for progress updates (current_step, total_steps)

        Returns:
            List of base64 encoded PNG image strings
        """
        if not self.pipeline:
            raise RuntimeError("Pipeline not loaded")

        # Enhance prompt for Pony models
        enhanced_prompt = self._enhance_prompt_for_pony(prompt)

        # Use model-specific default guidance scale if not explicitly set
        if guidance_scale == 7.5:  # Default value, use model-specific default
            guidance_scale = self._get_default_guidance_scale()

        print(f"🤖 Generating {image_count} image(s) with {self.model_type.upper()} model")
        if enhanced_prompt != prompt:
            print("🐴 Using enhanced prompt")

        # Log memory usage before generation
        if self.device == "cuda":
            self._log_memory_usage("Before generation")

        # Set seed for reproducibility
        if seed > 0:
            torch.manual_seed(seed)
            if self.device == "cuda":
                torch.cuda.manual_seed(seed)

        result = None
        base64_images = []

        try:
            # Generate the images
            with torch.no_grad():
                result = self.pipeline(
                    prompt=enhanced_prompt,
                    negative_prompt=negative_prompt if negative_prompt else None,
                    height=height,
                    width=width,
                    num_inference_steps=step_count,
                    guidance_scale=guidance_scale,
                    num_images_per_prompt=image_count,
                    callback=self._create_progress_callback(progress_callback, step_count),
                    callback_steps=1
                )

            # Convert images to base64 strings
            for i, image in enumerate(result.images):
                base64_str = self._image_to_base64(image)
                base64_images.append(base64_str)
                print(f"✓ Image {i+1}/{image_count} generated and encoded")

                # Clear the PIL image from memory immediately after encoding
                image.close()
                del image

            return base64_images

        except Exception as e:
            # Clean up on error
            self._cleanup_generation_memory(result)
            raise RuntimeError(f"Error during image generation: {e}")
        finally:
            # Always clean up memory after generation
            self._cleanup_generation_memory(result)

            # Log memory usage after cleanup
            if self.device == "cuda":
                self._log_memory_usage("After cleanup")
    
    def _create_progress_callback(self, progress_callback: Optional[Callable], total_steps: int):
        """Create a progress callback for the diffusion pipeline"""
        if not progress_callback:
            return None

        def callback(step: int, _timestep, _latents):
            progress_callback(step, total_steps)

        return callback
    
    def _image_to_base64(self, image: Image.Image) -> str:
        """Convert PIL Image to base64 encoded PNG string"""
        buffer = io.BytesIO()
        try:
            image.save(buffer, format="PNG")
            buffer.seek(0)
            image_bytes = buffer.getvalue()
            base64_str = base64.b64encode(image_bytes).decode('utf-8')
            return base64_str
        finally:
            # Ensure buffer is properly closed
            buffer.close()

    def _cleanup_generation_memory(self, result=None):
        """Clean up memory after image generation"""
        try:
            # Clear the result object if it exists
            if result is not None:
                # Clear any remaining images
                if hasattr(result, 'images') and result.images:
                    for img in result.images:
                        if hasattr(img, 'close'):
                            img.close()
                    result.images.clear()
                del result

            # Force garbage collection
            gc.collect()

            # Clear CUDA cache if using GPU
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

        except Exception as e:
            print(f"Warning: Error during memory cleanup: {e}")

    def _log_memory_usage(self, stage: str):
        """Log current memory usage for debugging"""
        if self.device == "cuda" and torch.cuda.is_available():
            allocated = torch.cuda.memory_allocated() / 1024**3
            reserved = torch.cuda.memory_reserved() / 1024**3
            print(f"🧠 Memory {stage}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved")
    
    def get_model_info(self) -> dict:
        """Get information about the loaded model"""
        return {
            "model_path": self.model_path,
            "model_type": self.model_type,
            "model_name": Path(self.model_path).name,
            "default_guidance_scale": self._get_default_guidance_scale(),
            "supports_quality_tags": self.model_type == "pony"
        }

    def get_device_info(self) -> dict:
        """Get information about the current device"""
        info = {
            "device": self.device,
            "cuda_available": torch.cuda.is_available()
        }

        if torch.cuda.is_available():
            info.update({
                "device_count": torch.cuda.device_count(),
                "current_device": torch.cuda.current_device(),
                "device_name": torch.cuda.get_device_name(torch.cuda.current_device()),
                "cuda_version": torch.version.cuda,
                "memory_allocated_gb": torch.cuda.memory_allocated() / 1024**3,
                "memory_reserved_gb": torch.cuda.memory_reserved() / 1024**3
            })

        return info

    def cleanup(self):
        """Clean up the pipeline and free GPU memory"""
        try:
            if self.pipeline is not None:
                # Move pipeline to CPU to free GPU memory
                if self.device == "cuda":
                    print("Moving pipeline to CPU for cleanup...")
                    self.pipeline = self.pipeline.to("cpu")

                # Delete the pipeline
                del self.pipeline
                self.pipeline = None

            # Force cleanup
            gc.collect()
            if self.device == "cuda" and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

            print("✓ ImageGenerator cleanup completed")

        except Exception as e:
            print(f"Warning: Error during ImageGenerator cleanup: {e}")

    def __del__(self):
        """Destructor to ensure cleanup when object is deleted"""
        self.cleanup()


def find_safetensors_file(search_dir: str = ".") -> Optional[str]:
    """Find the first .safetensors file in the given directory"""
    search_path = Path(search_dir)
    safetensors_files = list(search_path.rglob("*.safetensors"))
    
    if not safetensors_files:
        return None
    
    # Return the first one found
    return str(safetensors_files[0])
